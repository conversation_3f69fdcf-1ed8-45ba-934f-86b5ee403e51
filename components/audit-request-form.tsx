'use client';

import { useState } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Checkbox } from './ui/checkbox';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';

interface FormData {
    name: string;
    email: string;
    company: string;
    primaryGoals: string[];
    buildSucceeds: string;
    framework: string;
    frameworkVersion: string;
    hasReadme: string;
    hasNatspec: string;
    hasSequenceDiagrams: string;
    importedLibraries: string;
    commitsPinned: string;
}

export default function AuditRequestForm() {
    const [formData, setFormData] = useState<FormData>({
        name: '',
        email: '',
        company: '',
        primaryGoals: [],
        buildSucceeds: '',
        framework: '',
        frameworkVersion: '',
        hasReadme: '',
        hasNatspec: '',
        hasSequenceDiagrams: '',
        importedLibraries: '',
        commitsPinned: ''
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleInputChange = (field: keyof FormData, value: string) => {
        setFormData(prev => ({ ...prev, [field]: value }));
    };

    const handleGoalsChange = (goal: string, checked: boolean) => {
        setFormData(prev => ({
            ...prev,
            primaryGoals: checked
                ? [...prev.primaryGoals, goal]
                : prev.primaryGoals.filter(g => g !== goal)
        }));
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);

        // TODO: Implement form submission logic
        console.log('Form submitted:', formData);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
        setIsSubmitting(false);
    };

    return (
        <Card className="w-full max-w-4xl mx-auto">
            <CardHeader>
                <CardTitle className="text-2xl">Request Smart Contract Audit</CardTitle>
                <CardDescription>
                    Please provide details about your project to help us prepare the most effective audit for your smart contracts.
                </CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Basic Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Contact Information</h3>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="name">Name *</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    required
                                    placeholder="Your full name"
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="email">Email *</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={formData.email}
                                    onChange={(e) => handleInputChange('email', e.target.value)}
                                    required
                                    placeholder="<EMAIL>"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="company">Company</Label>
                            <Input
                                id="company"
                                type="text"
                                value={formData.company}
                                onChange={(e) => handleInputChange('company', e.target.value)}
                                placeholder="Your company or project name"
                            />
                        </div>
                    </div>

                    {/* Primary Goals */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Audit Objectives</h3>
                        <div className="space-y-2">
                            <Label>What are your primary goals? (Select all that apply)</Label>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {[
                                    'Security hardening',
                                    'Formal verification',
                                    'Gas optimization',
                                    'Compliance'
                                ].map((goal) => (
                                    <div key={goal} className="flex items-center space-x-2">
                                        <Checkbox
                                            id={goal}
                                            checked={formData.primaryGoals.includes(goal)}
                                            onCheckedChange={(checked) => handleGoalsChange(goal, checked as boolean)}
                                        />
                                        <Label htmlFor={goal} className="text-sm font-normal">
                                            {goal}
                                        </Label>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Build Status */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Build Configuration</h3>
                        <div className="space-y-4">
                            <div className="space-y-3">
                                <Label>Does forge build (or pnpm hardhat compile, foundry fmt, etc.) succeed from a clean clone? *</Label>
                                <RadioGroup
                                    value={formData.buildSucceeds}
                                    onValueChange={(value) => handleInputChange('buildSucceeds', value)}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="yes" id="build-yes" />
                                        <Label htmlFor="build-yes">Yes, builds successfully</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="no" id="build-no" />
                                        <Label htmlFor="build-no">No, there are build issues</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="partial" id="build-partial" />
                                        <Label htmlFor="build-partial">Partially - some components build</Label>
                                    </div>
                                </RadioGroup>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="framework">Framework *</Label>
                                    <Select value={formData.framework} onValueChange={(value) => handleInputChange('framework', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select framework" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="foundry">Foundry</SelectItem>
                                            <SelectItem value="hardhat">Hardhat</SelectItem>
                                            <SelectItem value="brownie">Brownie</SelectItem>
                                            <SelectItem value="other">Other</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="frameworkVersion">Framework Version</Label>
                                    <Input
                                        id="frameworkVersion"
                                        type="text"
                                        value={formData.frameworkVersion}
                                        onChange={(e) => handleInputChange('frameworkVersion', e.target.value)}
                                        placeholder="e.g., 0.2.0, 2.19.1"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Documentation */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Documentation & Code Quality</h3>
                        <div className="space-y-4">
                            <div className="space-y-3">
                                <Label>Is there a high-level README.md explaining protocol flow? *</Label>
                                <RadioGroup
                                    value={formData.hasReadme}
                                    onValueChange={(value) => handleInputChange('hasReadme', value)}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="comprehensive" id="readme-comprehensive" />
                                        <Label htmlFor="readme-comprehensive">Yes, comprehensive documentation</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="basic" id="readme-basic" />
                                        <Label htmlFor="readme-basic">Basic README exists</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="minimal" id="readme-minimal" />
                                        <Label htmlFor="readme-minimal">Minimal or no documentation</Label>
                                    </div>
                                </RadioGroup>
                            </div>

                            <div className="space-y-3">
                                <Label>Natspec / inline comments for non-obvious math? *</Label>
                                <RadioGroup
                                    value={formData.hasNatspec}
                                    onValueChange={(value) => handleInputChange('hasNatspec', value)}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="extensive" id="natspec-extensive" />
                                        <Label htmlFor="natspec-extensive">Extensive Natspec and comments</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="some" id="natspec-some" />
                                        <Label htmlFor="natspec-some">Some documentation</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="minimal" id="natspec-minimal" />
                                        <Label htmlFor="natspec-minimal">Minimal or no inline comments</Label>
                                    </div>
                                </RadioGroup>
                            </div>

                            <div className="space-y-3">
                                <Label>Sequence diagrams / state machines? *</Label>
                                <RadioGroup
                                    value={formData.hasSequenceDiagrams}
                                    onValueChange={(value) => handleInputChange('hasSequenceDiagrams', value)}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="yes" id="diagrams-yes" />
                                        <Label htmlFor="diagrams-yes">Yes, detailed diagrams available</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="partial" id="diagrams-partial" />
                                        <Label htmlFor="diagrams-partial">Some diagrams exist</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="no" id="diagrams-no" />
                                        <Label htmlFor="diagrams-no">No diagrams available</Label>
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </div>

                    {/* Dependencies */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Dependencies & Libraries</h3>
                        <div className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="importedLibraries">List of imported libraries (OpenZeppelin, Solmate, custom forks)? *</Label>
                                <Textarea
                                    id="importedLibraries"
                                    value={formData.importedLibraries}
                                    onChange={(e) => handleInputChange('importedLibraries', e.target.value)}
                                    placeholder="e.g., @openzeppelin/contracts@4.8.0, solmate@6.2.0, custom fork of Uniswap V3..."
                                    rows={4}
                                    required
                                />
                            </div>

                            <div className="space-y-3">
                                <Label>Are any commits pinned by hash? *</Label>
                                <RadioGroup
                                    value={formData.commitsPinned}
                                    onValueChange={(value) => handleInputChange('commitsPinned', value)}
                                >
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="yes" id="commits-yes" />
                                        <Label htmlFor="commits-yes">Yes, all dependencies pinned to specific commits</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="partial" id="commits-partial" />
                                        <Label htmlFor="commits-partial">Some dependencies pinned</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="no" id="commits-no" />
                                        <Label htmlFor="commits-no">No, using version ranges</Label>
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="pt-6 border-t">
                        <Button
                            type="submit"
                            className="w-full md:w-auto px-8 py-3"
                            disabled={isSubmitting}
                        >
                            {isSubmitting ? 'Submitting Request...' : 'Submit Audit Request'}
                        </Button>
                        <p className="text-sm text-muted-foreground mt-2">
                            We'll review your request and get back to you within 24 hours.
                        </p>
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}