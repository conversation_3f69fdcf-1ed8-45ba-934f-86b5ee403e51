export default function AuditRequestForm() {
    return (
        <Form className="space-y-4">
            <div className="space-y-2">
                <label htmlFor="name">Full Name</label>
                <input type="text" id="name" className="w-full border border-gray-300 rounded-md p-2" />
            </div>
            <div className="space-y-2">
                <label htmlFor="email">Email</label>    
                <input type="email" id="email" className="w-full border border-gray-300 rounded-md p-2" />
            </div>
            <div className="space-y-2">
                <label htmlFor="company">Company (Optional)</label>
                <input type="text" id="company" className="w-full border border-gray-300 rounded-md p-2" />
            </div>
            <div className="space-y-2">
                <label htmlFor="message">Message</label>
                <textarea id="message" className="w-full border border-gray-300 rounded-md p-2" />
            </div>
            <button type="submit" className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Submit
            </button>
        </>
    );
}