'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox';
import { RadioGroup, RadioGroupItem } from './ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from './ui/form';

const formSchema = z.object({
    name: z.string().min(2, 'Name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    company: z.string().optional(),
    primaryGoals: z.array(z.string()).min(1, 'Please select at least one goal'),
    buildSucceeds: z.string().min(1, 'Please select build status'),
    framework: z.string().min(1, 'Please select a framework'),
    frameworkVersion: z.string().optional(),
    hasReadme: z.string().min(1, 'Please select README status'),
    hasNatspec: z.string().min(1, 'Please select Natspec status'),
    hasSequenceDiagrams: z.string().min(1, 'Please select diagram status'),
    importedLibraries: z.string().min(10, 'Please provide details about imported libraries'),
    commitsPinned: z.string().min(1, 'Please select commit pinning status'),
});

type FormData = z.infer<typeof formSchema>;

export default function AuditRequestForm() {
    const form = useForm<FormData>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
            email: '',
            company: '',
            primaryGoals: [],
            buildSucceeds: '',
            framework: '',
            frameworkVersion: '',
            hasReadme: '',
            hasNatspec: '',
            hasSequenceDiagrams: '',
            importedLibraries: '',
            commitsPinned: '',
        },
    });

    const onSubmit = async (data: FormData) => {
        // TODO: Implement form submission logic
        console.log('Form submitted:', data);

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000));
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Basic Information */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Contact Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Name *</FormLabel>
                                    <FormControl>
                                        <Input placeholder="Your full name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Email *</FormLabel>
                                    <FormControl>
                                        <Input placeholder="<EMAIL>" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>

                    <FormField
                        control={form.control}
                        name="company"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Company</FormLabel>
                                <FormControl>
                                    <Input placeholder="Your company or project name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Primary Goals */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Audit Objectives</h3>
                    <FormField
                        control={form.control}
                        name="primaryGoals"
                        render={() => (
                            <FormItem>
                                <FormLabel>What are your primary goals? (Select all that apply)</FormLabel>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    {[
                                        'Security hardening',
                                        'Formal verification',
                                        'Gas optimization',
                                        'Compliance'
                                    ].map((goal) => (
                                        <FormField
                                            key={goal}
                                            control={form.control}
                                            name="primaryGoals"
                                            render={({ field }) => {
                                                return (
                                                    <FormItem
                                                        key={goal}
                                                        className="flex flex-row items-start space-x-3 space-y-0"
                                                    >
                                                        <FormControl>
                                                            <Checkbox
                                                                checked={field.value?.includes(goal)}
                                                                onCheckedChange={(checked) => {
                                                                    return checked
                                                                        ? field.onChange([...field.value, goal])
                                                                        : field.onChange(
                                                                            field.value?.filter(
                                                                                (value) => value !== goal
                                                                            )
                                                                        )
                                                                }}
                                                            />
                                                        </FormControl>
                                                        <FormLabel className="text-sm font-normal">
                                                            {goal}
                                                        </FormLabel>
                                                    </FormItem>
                                                )
                                            }}
                                        />
                                    ))}
                                </div>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                {/* Build Status */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Build Configuration</h3>
                    <div className="space-y-4">
                        <FormField
                            control={form.control}
                            name="buildSucceeds"
                            render={({ field }) => (
                                <FormItem className="space-y-3">
                                    <FormLabel>Does forge build (or pnpm hardhat compile, foundry fmt, etc.) succeed from a clean clone? *</FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                            className="flex flex-col space-y-1"
                                        >
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="yes" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Yes, builds successfully
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="no" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    No, there are build issues
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="partial" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Partially - some components build
                                                </FormLabel>
                                            </FormItem>
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="framework"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Framework *</FormLabel>
                                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                                            <FormControl>
                                                <SelectTrigger>
                                                    <SelectValue placeholder="Select framework" />
                                                </SelectTrigger>
                                            </FormControl>
                                            <SelectContent>
                                                <SelectItem value="foundry">Foundry</SelectItem>
                                                <SelectItem value="hardhat">Hardhat</SelectItem>
                                                <SelectItem value="brownie">Brownie</SelectItem>
                                                <SelectItem value="other">Other</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="frameworkVersion"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Framework Version</FormLabel>
                                        <FormControl>
                                            <Input placeholder="e.g., 0.2.0, 2.19.1" {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </div>
                </div>

                {/* Documentation */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Documentation & Code Quality</h3>
                    <div className="space-y-4">
                        <FormField
                            control={form.control}
                            name="hasReadme"
                            render={({ field }) => (
                                <FormItem className="space-y-3">
                                    <FormLabel>Is there a high-level README.md explaining protocol flow? *</FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                            className="flex flex-col space-y-1"
                                        >
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="comprehensive" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Yes, comprehensive documentation
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="basic" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Basic README exists
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="minimal" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Minimal or no documentation
                                                </FormLabel>
                                            </FormItem>
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="hasNatspec"
                            render={({ field }) => (
                                <FormItem className="space-y-3">
                                    <FormLabel>Natspec / inline comments for non-obvious math? *</FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                            className="flex flex-col space-y-1"
                                        >
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="extensive" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Extensive Natspec and comments
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="some" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Some documentation
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="minimal" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Minimal or no inline comments
                                                </FormLabel>
                                            </FormItem>
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="hasSequenceDiagrams"
                            render={({ field }) => (
                                <FormItem className="space-y-3">
                                    <FormLabel>Sequence diagrams / state machines? *</FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                            className="flex flex-col space-y-1"
                                        >
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="yes" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Yes, detailed diagrams available
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="partial" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Some diagrams exist
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="no" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    No diagrams available
                                                </FormLabel>
                                            </FormItem>
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                {/* Dependencies */}
                <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Dependencies & Libraries</h3>
                    <div className="space-y-4">
                        <FormField
                            control={form.control}
                            name="importedLibraries"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>List of imported libraries (OpenZeppelin, Solmate, custom forks)? *</FormLabel>
                                    <FormControl>
                                        <Textarea
                                            placeholder="e.g., @openzeppelin/contracts@4.8.0, solmate@6.2.0, custom fork of Uniswap V3..."
                                            className="resize-none"
                                            {...field}
                                        />
                                    </FormControl>
                                    <FormDescription>
                                        Please list all external dependencies with versions
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="commitsPinned"
                            render={({ field }) => (
                                <FormItem className="space-y-3">
                                    <FormLabel>Are any commits pinned by hash? *</FormLabel>
                                    <FormControl>
                                        <RadioGroup
                                            onValueChange={field.onChange}
                                            defaultValue={field.value}
                                            className="flex flex-col space-y-1"
                                        >
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="yes" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Yes, all dependencies pinned to specific commits
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="partial" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    Some dependencies pinned
                                                </FormLabel>
                                            </FormItem>
                                            <FormItem className="flex items-center space-x-3 space-y-0">
                                                <FormControl>
                                                    <RadioGroupItem value="no" />
                                                </FormControl>
                                                <FormLabel className="font-normal">
                                                    No, using version ranges
                                                </FormLabel>
                                            </FormItem>
                                        </RadioGroup>
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </div>

                {/* Submit Button */}
                <div className="pt-6 border-t">
                    <Button
                        type="submit"
                        className="w-full md:w-auto px-8 py-3"
                        disabled={form.formState.isSubmitting}
                    >
                        {form.formState.isSubmitting ? 'Submitting Request...' : 'Submit Audit Request'}
                    </Button>
                    <p className="text-sm text-muted-foreground mt-2">
                        We'll review your request and get back to you within 24 hours.
                    </p>
                </div>
            </form>
        </Form>
    );
}